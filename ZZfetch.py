# -*- coding: gbk -*-
"""
ZZfetch.py - 股票数据获取模块
从 Zstock_tech_qmttdx.py 提取并重构的核心数据获取功能

主要功能：
1. 从 MiniQMT 获取个股多时间周期K线数据
2. 从本地TDX文件获取资金面数据
3. 从本地TDX文件获取板块指数数据
4. 从AkShare获取新闻数据

作者：基于 Zstock_tech_qmttdx.py 重构
日期：2025-01-28
"""

import pandas as pd
import numpy as np
import sys
import os
import re
import locale
import struct
from datetime import datetime, timedelta
import time
import glob
import importlib
from ZZconfig import DataConfig

# 全局变量
MINIQMT_CONNECTED = False
XT_TRADER = None

try:
    from xtquant import xtdata
    XTDATA_AVAILABLE = True
except ImportError:
    try:
        import xtdata
        XTDATA_AVAILABLE = True
    except ImportError:
        XTDATA_AVAILABLE = False
    print("警告: xtdata模块未安装，MiniQMT功能将不可用")

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("警告: akshare模块未安装，新闻获取功能将不可用")


class DataFetcher:
    """数据获取器主类"""

    def __init__(self):
        """初始化数据获取器"""
        self.config = DataConfig()
        self.miniqmt_connected = False
        self.xt_trader = None
        self._cache = {}

    def connect_miniqmt(self):
        """连接MiniQMT"""
        global MINIQMT_CONNECTED, XT_TRADER

        if not XTDATA_AVAILABLE:
            print("错误: xtdata模块未安装，无法连接MiniQMT")
            return False

        if MINIQMT_CONNECTED:
            print("MiniQMT已连接")
            return True

        try:
            print("连接QMT...")
            # 直接使用xtdata.connect()方法（与原始程序相同）
            connect_result = xtdata.connect()
            print(f"连接结果: {connect_result}")

            if connect_result:
                print("MiniQMT连接成功")
                MINIQMT_CONNECTED = True
                self.miniqmt_connected = True
                return True
            else:
                print("MiniQMT连接失败，请检查QMT客户端是否运行")
                return False

        except Exception as e:
            print(f"MiniQMT连接异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def disconnect_miniqmt(self):
        """断开MiniQMT连接"""
        global MINIQMT_CONNECTED, XT_TRADER

        try:
            if XTDATA_AVAILABLE and MINIQMT_CONNECTED:
                xtdata.disconnect()
                MINIQMT_CONNECTED = False
                self.miniqmt_connected = False
                print("MiniQMT连接已断开")
        except Exception as e:
            print(f"断开MiniQMT连接时出错: {e}")

    def _format_stock_code(self, stock_code):
        """格式化股票代码为MiniQMT格式"""
        if not stock_code or len(stock_code) != 6:
            return None

        # 清理代码
        clean_code = self.config.clean_stock_code(stock_code)

        # 添加市场后缀
        if clean_code.startswith(('000', '002', '003', '300')):
            return f"{clean_code}.SZ"
        elif clean_code.startswith(('600', '601', '603', '605', '688')):
            return f"{clean_code}.SH"
        else:
            # 默认深圳
            return f"{clean_code}.SZ"

    def _download_data_if_needed(self, formatted_code, period):
        """检查数据新鲜度，如果需要则下载最新数据"""
        try:
            from datetime import datetime, timedelta

            # 先获取当前缓存的数据检查新鲜度
            test_data = xtdata.get_market_data(
                field_list=['time'],
                stock_list=[formatted_code],
                period=period,
                count=1,
                dividend_type='front',
                fill_data=True
            )

            if test_data and 'time' in test_data:
                time_data = test_data['time']
                if isinstance(time_data, pd.DataFrame) and formatted_code in time_data.columns:
                    latest_timestamp = time_data[formatted_code].iloc[-1]
                elif isinstance(time_data, pd.Series):
                    latest_timestamp = time_data.iloc[-1]
                else:
                    # 无法获取时间戳，直接下载
                    self._download_history_data(formatted_code, period)
                    return

                # 转换时间戳
                try:
                    latest_date = pd.to_datetime(latest_timestamp, unit='s', utc=True).tz_convert('Asia/Shanghai').date()
                except:
                    latest_date = pd.to_datetime(latest_timestamp, unit='ms', utc=True).tz_convert('Asia/Shanghai').date()

                current_date = datetime.now().date()
                days_behind = (current_date - latest_date).days

                # 如果数据滞后超过1天，下载最新数据
                if days_behind > 1:
                    print(f"??  检测到数据滞后{days_behind}天，正在下载最新数据...")
                    self._download_history_data(formatted_code, period)

        except Exception as e:
            # 如果检查失败，直接下载数据
            print(f"数据新鲜度检查失败，下载最新数据: {e}")
            self._download_history_data(formatted_code, period)

    def _download_history_data(self, formatted_code, period):
        """下载历史数据到本地缓存"""
        try:
            from datetime import datetime, timedelta

            today = datetime.now()
            today_str = today.strftime('%Y%m%d')

            if period in ['1d', '1w']:
                # 日线和周线下载半年数据
                start_date = (today - timedelta(days=180)).strftime('%Y%m%d')
            elif period == '30m':
                # 30分钟数据需要先下载5分钟数据
                start_date = (today - timedelta(days=14)).strftime('%Y%m%d')
                # 先下载5分钟数据
                xtdata.download_history_data(
                    stock_code=formatted_code,
                    period='5m',
                    start_time=start_date,
                    end_time=today_str
                )
                # 再下载30分钟数据
                xtdata.download_history_data(
                    stock_code=formatted_code,
                    period='30m',
                    start_time=start_date,
                    end_time=today_str
                )
                return
            elif period == '5m':
                # 5分钟数据下载14天
                start_date = (today - timedelta(days=14)).strftime('%Y%m%d')
            else:
                # 其他周期下载30天
                start_date = (today - timedelta(days=30)).strftime('%Y%m%d')

            xtdata.download_history_data(
                stock_code=formatted_code,
                period=period,
                start_time=start_date,
                end_time=today_str
            )

        except Exception as e:
            print(f"下载数据异常: {e}")

    def _get_miniqmt_data(self, stock_code, period='1d', count=250):
        """从MiniQMT获取K线数据的核心实现（增强版本）"""
        if not XTDATA_AVAILABLE:
            print("错误: xtdata模块未安装，无法连接MiniQMT")
            return pd.DataFrame()

        if not self.miniqmt_connected:
            print("错误: MiniQMT未连接")
            return pd.DataFrame()

        try:
            # 格式化股票代码为带交易所后缀的格式
            if stock_code.lower().startswith(('sh', 'sz')):
                code_only = stock_code[2:]
            else:
                code_only = stock_code

            # 添加交易所后缀
            if code_only.startswith('6'):
                formatted_code = f"{code_only}.SH"  # 上海证券交易所
            elif code_only.startswith(('0', '3')):
                formatted_code = f"{code_only}.SZ"  # 深圳证券交易所
            elif code_only.startswith(('880', '881')):
                formatted_code = f"{code_only}.SH"  # 板块指数（上海）
            elif code_only.startswith('399'):
                formatted_code = f"{code_only}.SZ"  # 深圳指数
            else:
                print(f"无法识别的股票代码格式: {stock_code}")
                return pd.DataFrame()

            # 检查数据新鲜度并下载最新数据
            self._download_data_if_needed(formatted_code, period)

            # 获取K线数据
            kline_data = xtdata.get_market_data(
                field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
                stock_list=[formatted_code],
                period=period,
                count=count,
                dividend_type='front',
                fill_data=True
            )

            # 检查是否成功获取数据
            if kline_data is None:
                print(f"无法获取{formatted_code}的数据")
                return pd.DataFrame()

            # 关键修改：统一处理时间数据格式
            # 获取时间数据并转换为标准格式
            time_data = kline_data.get('time')

            # 检查时间数据是否有效
            if time_data is None:
                print(f"未获取到{formatted_code}的时间数据")
                return pd.DataFrame()

            # 统一处理时间数据格式
            if isinstance(time_data, pd.DataFrame):
                # DataFrame格式 - 提取股票代码对应的时间列
                if formatted_code in time_data.columns:
                    time_series = time_data[formatted_code]
                elif formatted_code in time_data.index:
                    time_series = time_data.loc[formatted_code]
                else:
                    print(f"无法在时间数据中找到股票代码 {formatted_code}")
                    return pd.DataFrame()
            elif isinstance(time_data, pd.Series):
                # Series格式 - 直接使用
                time_series = time_data
            else:
                print(f"无法识别的时间数据类型: {type(time_data)}")
                return pd.DataFrame()

            # === 修复时间戳解析 ===
            try:
                # 尝试秒级解析 (QMT可能返回秒级时间戳)
                utc_dates = pd.to_datetime(time_series, unit='s', utc=True)
            except:
                # 尝试毫秒级解析 (备用方案)
                utc_dates = pd.to_datetime(time_series, unit='ms', utc=True)

            # 转换为本地时间并移除时区信息
            dates = utc_dates.dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
            # === 时间戳修复结束 ===

            # 创建结果DataFrame
            result_df = pd.DataFrame({
                '日期': dates
            })

            # 添加数据完整性检查
            required_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for field in required_columns:
                if field in kline_data:
                    field_data = kline_data[field]

                    # 统一处理字段数据格式
                    if isinstance(field_data, pd.DataFrame):
                        # DataFrame格式 - 提取股票代码对应的列
                        if formatted_code in field_data.columns:
                            result_df[field] = field_data[formatted_code].values
                        elif formatted_code in field_data.index:
                            result_df[field] = field_data.loc[formatted_code].values
                        else:
                            print(f"无法在{field}数据中找到股票代码 {formatted_code}")
                            result_df[field] = 0.0
                    elif isinstance(field_data, pd.Series):
                        # Series格式 - 直接使用
                        result_df[field] = field_data.values
                    else:
                        print(f"无法识别的{field}数据类型: {type(field_data)}")
                        result_df[field] = 0.0
                else:
                    print(f"警告: {field}字段不存在")
                    result_df[field] = 0.0

            # 统一列名
            result_df.rename(columns={
                'open': '开盘',
                'high': '最高',
                'low': '最低',
                'close': '收盘',
                'volume': '成交量',
                'amount': '成交额',
                'date': '日期',
                'time': '日期'  # 确保time列也被重命名为日期
            }, inplace=True)

            # 确保列名已被统一转换为中文
            for en, cn in [('open', '开盘'), ('high', '最高'), ('low', '最低'),
                           ('close', '收盘'), ('volume', '成交量'), ('amount', '成交额')]:
                if en in result_df.columns and cn not in result_df.columns:
                    result_df.rename(columns={en: cn}, inplace=True)

            # 检查结果DataFrame是否为空
            if result_df.empty:
                return pd.DataFrame()

            # 按日期升序排列 (旧->新) 确保最新数据在最后
            result_df = result_df.sort_values('日期', ascending=True).reset_index(drop=True)

            return result_df

        except Exception as e:
            print(f"从MiniQMT获取数据出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()

    def get_stock_daily_data(self, stock_code, count=None):
        """获取个股日线数据"""
        if count is None:
            count = self.config.DEFAULT_PARAMS['daily_count']

        start_time = time.time()
        df = self._get_miniqmt_data(stock_code, period='1d', count=count)
        end_time = time.time()

        if self.config.DEBUG_CONFIG['enable_debug']:
            self._print_data_info("日线数据", df, end_time - start_time)

        return df

    def _convert_daily_to_weekly(self, daily_data):
        """将日线数据转换为周线数据，确保时间戳以周五为准"""
        if daily_data.empty:
            return pd.DataFrame()

        try:
            # 确保日期列是datetime类型
            daily_data_copy = daily_data.copy()
            date_col = '日期'

            if date_col not in daily_data_copy.columns:
                print("错误: 日线数据缺少日期列")
                return pd.DataFrame()

            # 转换日期格式
            daily_data_copy[date_col] = pd.to_datetime(daily_data_copy[date_col])

            # 按日期排序
            daily_data_copy = daily_data_copy.sort_values(date_col)

            # 添加ISO周数和年份
            try:
                # pandas 1.1.0及以上版本的写法
                daily_data_copy['year_week'] = daily_data_copy[date_col].dt.isocalendar().year.astype(str) + '-' + \
                                              daily_data_copy[date_col].dt.isocalendar().week.astype(str)
            except:
                # 兼容旧版本pandas的备用方案
                daily_data_copy['year_week'] = daily_data_copy[date_col].apply(
                    lambda x: f"{x.isocalendar()[0]}-{x.isocalendar()[1]}")

            # 按周分组
            grouped = daily_data_copy.groupby('year_week')
            result_data = []

            for _, group in grouped:
                # 确保组内数据按日期排序
                group = group.sort_values(date_col)

                # 寻找周五的日期作为周线时间戳
                friday = None
                for date_val in group[date_col]:
                    if date_val.weekday() == 4:  # 周五
                        friday = date_val
                        break

                # 如果没有周五，使用该周的最后一个交易日
                if friday is None:
                    last_date = group[date_col].max()
                    # 如果是当前周且还没到周五，使用最后交易日
                    # 但标记为未完成周
                    friday = last_date

                # 计算周线数据
                first_open = group['开盘'].iloc[0]
                highest = group['最高'].max()
                lowest = group['最低'].min()
                last_close = group['收盘'].iloc[-1]
                volume_sum = group['成交量'].sum()
                amount_sum = group['成交额'].sum() if '成交额' in group.columns else 0

                result_data.append({
                    '日期': friday,
                    '开盘': first_open,
                    '最高': highest,
                    '最低': lowest,
                    '收盘': last_close,
                    '成交量': volume_sum,
                    '成交额': amount_sum
                })

            # 创建周线DataFrame
            weekly_df = pd.DataFrame(result_data)

            # 按日期排序（保持与原始数据相同的排序方式）
            weekly_df = weekly_df.sort_values('日期', ascending=True).reset_index(drop=True)

            return weekly_df

        except Exception as e:
            print(f"转换周线数据时出错: {e}")
            return pd.DataFrame()

    def get_stock_weekly_data(self, stock_code, count=None):
        """获取个股周线数据（优化版本，确保时间戳正确）"""
        if count is None:
            count = self.config.DEFAULT_PARAMS['weekly_count']

        start_time = time.time()

        # 方法1：尝试直接获取周线数据
        df = self._get_miniqmt_data(stock_code, period='1w', count=count)

        # 方法2：如果直接获取的周线数据时间戳有问题，使用日线转换
        if not df.empty and '日期' in df.columns:
            # 检查时间戳是否正确（应该是周五或该周最后交易日）
            sample_date = df['日期'].iloc[-1]
            if hasattr(sample_date, 'weekday'):
                weekday = sample_date.weekday()
                # 如果不是周五(4)且不是周四(3)，可能时间戳有问题
                if weekday not in [3, 4]:  # 周四或周五
                    print("??  检测到周线时间戳可能不准确，使用日线数据转换...")
                    # 获取更多日线数据来转换
                    daily_count = count * 7 if count else 350  # 确保有足够的日线数据
                    daily_df = self._get_miniqmt_data(stock_code, period='1d', count=daily_count)
                    if not daily_df.empty:
                        converted_df = self._convert_daily_to_weekly(daily_df)
                        if not converted_df.empty and len(converted_df) >= count:
                            df = converted_df.tail(count).reset_index(drop=True)

        end_time = time.time()

        if self.config.DEBUG_CONFIG['enable_debug']:
            self._print_data_info("周线数据", df, end_time - start_time)

        return df

    def get_stock_30min_data(self, stock_code, count=None):
        """获取个股30分钟数据"""
        if count is None:
            count = self.config.DEFAULT_PARAMS['min30_count']

        start_time = time.time()
        df = self._get_miniqmt_data(stock_code, period='30m', count=count)
        end_time = time.time()

        if self.config.DEBUG_CONFIG['enable_debug']:
            self._print_data_info("30分钟数据", df, end_time - start_time)

        return df

    def get_stock_5min_data(self, stock_code, count=None):
        """获取个股5分钟数据"""
        if count is None:
            count = self.config.DEFAULT_PARAMS['min5_count']

        start_time = time.time()
        df = self._get_miniqmt_data(stock_code, period='5m', count=count)
        end_time = time.time()

        if self.config.DEBUG_CONFIG['enable_debug']:
            self._print_data_info("5分钟数据", df, end_time - start_time)

        return df

    def get_capital_flow_data(self, stock_code, days=None):
        """从本地TDX文件获取资金面数据"""
        if days is None:
            days = self.config.DEFAULT_PARAMS['capital_days']

        start_time = time.time()

        try:
            base_dir = self.config.get_tdx_capital_dir()
            if not base_dir:
                print("未找到TDX资金面数据目录")
                return []

            # 判断是股票还是板块
            is_block = self.config.is_block_code(stock_code)

            if is_block:
                # 板块代码：查找板块指数文件
                all_files = glob.glob(os.path.join(base_dir, "板块指数*.xls")) + \
                           glob.glob(os.path.join(base_dir, "板块指数*.txt"))
            else:
                # 股票代码：查找全部A股文件
                all_files = glob.glob(os.path.join(base_dir, "全部Ａ股*.xls")) + \
                           glob.glob(os.path.join(base_dir, "全部Ａ股*.txt"))

            if not all_files:
                file_type = "板块指数" if is_block else "全部Ａ股"
                print(f"在目录 {base_dir} 中未找到{file_type}资金面数据文件")
                return []

            # 按日期排序文件
            sorted_files = sorted(all_files, key=self._extract_date_from_filename, reverse=True)
            recent_files = sorted_files[:days]

            capital_data = []

            for file_path in recent_files:
                file_date = self._extract_date_from_filename(file_path)
                date_str = file_date.strftime('%Y-%m-%d')

                # 尝试读取文件
                stock_row = self._read_capital_file(file_path, stock_code)

                if stock_row:
                    capital_info = self._parse_capital_data(stock_row, date_str)
                    capital_data.append(capital_info)

            end_time = time.time()

            if self.config.DEBUG_CONFIG['enable_debug']:
                print(f"资金面数据获取完成，共{len(capital_data)}条记录，耗时{end_time-start_time:.2f}秒")
                if capital_data and self.config.DEBUG_CONFIG['show_data_sample']:
                    print("样本数据:")
                    # 资金面数据只显示3条
                    for i, data in enumerate(capital_data[:3]):
                        print(f"  {i+1}. {data}")

            return capital_data

        except Exception as e:
            print(f"获取资金面数据时出错: {e}")
            return []

    def _extract_date_from_filename(self, filename):
        """从文件名中提取日期"""
        # 尝试多种格式的日期提取
        patterns = [
            r'全部Ａ股(\d{8})',
            r'板块指数(\d{8})',
            r'沪深A股(\d{8})',
            r'全部股票(\d{8})',
            r'(\d{8})'
        ]

        for pattern in patterns:
            date_match = re.search(pattern, os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                try:
                    return datetime.strptime(date_str, '%Y%m%d')
                except ValueError:
                    continue

        return datetime.min

    def _read_capital_file(self, file_path, stock_code):
        """读取资金面数据文件"""
        try:
            # 检查是否为真正的Excel文件
            is_real_excel = False
            if file_path.lower().endswith('.xls'):
                try:
                    with open(file_path, 'rb') as f:
                        header = f.read(8)
                        is_real_excel = header.startswith(b'\xD0\xCF\x11\xE0') or header.startswith(b'PK\x03\x04')
                except Exception:
                    is_real_excel = False

            if is_real_excel:
                # Excel格式读取
                df = pd.read_excel(file_path, engine='xlrd')
                return self._find_stock_in_dataframe(df, stock_code)
            else:
                # 文本格式读取
                return self._read_capital_text_file(file_path, stock_code)

        except Exception as e:
            print(f"读取文件 {file_path} 失败: {e}")
            return None

    def _find_stock_in_dataframe(self, df, stock_code):
        """在DataFrame中查找股票数据"""
        code_columns = ['代码', '指数代码', '板块代码', '股票代码']

        for _, row in df.iterrows():
            for code_col in code_columns:
                if code_col in df.columns and pd.notna(row[code_col]):
                    row_code = self.config.clean_stock_code(str(row[code_col]))
                    if row_code == stock_code:
                        return row.to_dict()
        return None

    def _read_capital_text_file(self, file_path, stock_code):
        """读取文本格式的资金面数据文件"""
        try:
            with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                lines = f.readlines()

            if not lines:
                return None

            # 解析表头
            header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
            if not header:
                return None

            # 查找股票数据
            code_columns = ['代码', '指数代码', '板块代码', '股票代码']

            for line in lines[1:]:
                if not line.strip():
                    continue

                values = [val.strip() for val in line.strip().split('\t')]
                if len(values) >= len(header):
                    row_data = dict(zip(header, values))

                    for code_col in code_columns:
                        if code_col in row_data and row_data[code_col]:
                            row_code = self.config.clean_stock_code(str(row_data[code_col]))
                            if row_code == stock_code:
                                return row_data
            return None

        except Exception as e:
            print(f"读取文本文件 {file_path} 失败: {e}")
            return None

    def _parse_capital_data(self, stock_row, date_str):
        """解析资金面数据"""
        capital_info = {
            'date': date_str,
            'main_flow': None,
            'main_buy': None,
            'inner_volume': None,
            'outer_volume': None
        }

        for key, value in stock_row.items():
            if pd.notna(value) and value:
                key_lower = str(key).lower()

                # 主力净额（排除时间周期列）
                if '主力净额' in key_lower and '占比' not in key_lower and '分钟' not in key_lower:
                    try:
                        capital_info['main_flow'] = float(str(value).replace(',', ''))
                    except (ValueError, TypeError):
                        capital_info['main_flow'] = 0

                # 主买净额
                elif '主买净额' in key_lower and '分钟' not in key_lower:
                    try:
                        capital_info['main_buy'] = float(str(value).replace(',', ''))
                    except (ValueError, TypeError):
                        capital_info['main_buy'] = 0

                # 内盘成交量
                elif '内盘' in key_lower and '外盘' not in key_lower:
                    try:
                        capital_info['inner_volume'] = float(str(value).replace(',', ''))
                    except (ValueError, TypeError):
                        capital_info['inner_volume'] = 0

                # 外盘成交量（避免匹配到'内外盘比'）
                elif '外盘' in key_lower and '内' not in key_lower:
                    try:
                        capital_info['outer_volume'] = float(str(value).replace(',', ''))
                    except (ValueError, TypeError):
                        capital_info['outer_volume'] = 0

        return capital_info

    def get_block_daily_data(self, block_code, count=None):
        """从本地TDX文件获取板块指数日K线数据"""
        if count is None:
            count = self.config.DEFAULT_PARAMS['daily_count']

        start_time = time.time()

        try:
            base_dir = self.config.get_tdx_base_dir()
            if not base_dir:
                print("未找到TDX数据目录")
                return pd.DataFrame()

            # 确保是6位数字格式
            clean_code = self.config.clean_stock_code(block_code)
            if len(clean_code) != 6:
                print(f"无效的板块代码: {block_code}")
                return pd.DataFrame()

            # 板块数据文件路径（根据stock_analyzer.py的逻辑）
            possible_paths = [
                # 标准TDX板块数据路径
                os.path.join(base_dir, "vipdoc", "sh", "lday", f"sh{clean_code}.day"),
                os.path.join(base_dir, "vipdoc", "sz", "lday", f"sz{clean_code}.day"),
                # 备用路径
                os.path.join(base_dir, "sh", "lday", f"sh{clean_code}.day"),
                os.path.join(base_dir, "sz", "lday", f"sz{clean_code}.day"),
                os.path.join(base_dir, "lday", f"sh{clean_code}.day"),
                os.path.join(base_dir, "lday", f"sz{clean_code}.day")
            ]

            file_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    file_path = path
                    break

            if not file_path:
                print(f"未找到板块代码 {block_code} 的数据文件")
                return pd.DataFrame()

            # 读取TDX日线数据
            df = self._read_tdx_day_file(file_path, count)

            end_time = time.time()

            if self.config.DEBUG_CONFIG['enable_debug']:
                self._print_data_info("板块日线数据", df, end_time - start_time)

            return df

        except Exception as e:
            print(f"获取板块日线数据时出错: {e}")
            return pd.DataFrame()

    def _read_tdx_day_file(self, file_path, count):
        """读取TDX日线数据文件"""
        try:
            file_size = os.path.getsize(file_path)
            record_size = 32  # 日线数据固定32字节记录

            total_records = file_size // record_size
            if total_records == 0:
                return pd.DataFrame()

            data_list = []

            with open(file_path, 'rb') as f:
                # 从文件末尾开始读取指定数量的记录
                start_record = max(0, total_records - count)
                f.seek(start_record * record_size)

                record_count = 0
                while record_count < count:
                    record_data = f.read(record_size)
                    if len(record_data) < record_size:
                        break

                    try:
                        # 解析TDX日线数据结构
                        date_val = struct.unpack('<I', record_data[0:4])[0]
                        open_price = struct.unpack('<I', record_data[4:8])[0] / 100.0
                        high_price = struct.unpack('<I', record_data[8:12])[0] / 100.0
                        low_price = struct.unpack('<I', record_data[12:16])[0] / 100.0
                        close_price = struct.unpack('<I', record_data[16:20])[0] / 100.0
                        amount = struct.unpack('<f', record_data[20:24])[0]
                        volume = struct.unpack('<I', record_data[24:28])[0]

                        # 计算日期
                        if date_val > 0:
                            year = date_val // 10000
                            month = (date_val % 10000) // 100
                            day = date_val % 100

                            if (1990 <= year <= 2030 and 1 <= month <= 12 and 1 <= day <= 31 and
                                0.01 <= close_price <= 100000):

                                dt = datetime(year, month, day)

                                data_list.append({
                                    '日期': dt,
                                    '开盘': open_price,
                                    '最高': high_price,
                                    '最低': low_price,
                                    '收盘': close_price,
                                    '成交量': volume,
                                    '成交额': amount
                                })
                                record_count += 1
                    except (ValueError, struct.error):
                        continue

            if not data_list:
                return pd.DataFrame()

            # 创建DataFrame并排序
            df = pd.DataFrame(data_list)
            df = df.sort_values('日期', ascending=True).reset_index(drop=True)

            # 过滤异常数据
            df = df[df['收盘'] > 0]
            df = df[df['成交量'] >= 0]

            return df

        except Exception as e:
            print(f"读取TDX日线数据文件出错: {e}")
            return pd.DataFrame()



    def _print_data_info(self, data_type, df, elapsed_time):
        """打印数据信息"""
        if not self.config.DEBUG_CONFIG['enable_debug']:
            return

        print(f"\n=== {data_type} 获取结果 ===")

        if df.empty:
            print("  数据为空")
            return

        # 基本信息
        print(f"  数据量: {len(df)} 条记录")

        if self.config.DEBUG_CONFIG['show_timing']:
            print(f"  耗时: {elapsed_time:.2f} 秒")

        if self.config.DEBUG_CONFIG['show_data_info'] and len(df) > 0:
            # 时间范围
            if '日期' in df.columns:
                start_date = df['日期'].iloc[0]
                end_date = df['日期'].iloc[-1]
                print(f"  时间范围: {start_date} 至 {end_date}")

            # 列信息
            print(f"  数据列: {list(df.columns)}")

            # 样本数据
            if self.config.DEBUG_CONFIG['show_data_sample']:
                sample_rows = min(self.config.DEBUG_CONFIG['sample_rows'], len(df))
                print(f"  样本数据 (最新{sample_rows}条):")
                for i in range(sample_rows):
                    row = df.iloc[-(i+1)]  # 从最新的开始显示
                    if '日期' in df.columns:
                        date_str = row['日期'].strftime('%Y-%m-%d') if hasattr(row['日期'], 'strftime') else str(row['日期'])
                        print(f"    {date_str}: 开={row.get('开盘', 'N/A'):.2f}, "
                              f"高={row.get('最高', 'N/A'):.2f}, "
                              f"低={row.get('最低', 'N/A'):.2f}, "
                              f"收={row.get('收盘', 'N/A'):.2f}, "
                              f"量={row.get('成交量', 'N/A')}")

        print("=" * (len(data_type) + 12))


# 全局数据获取器实例
_global_fetcher = None

def get_fetcher():
    """获取全局数据获取器实例"""
    global _global_fetcher
    if _global_fetcher is None:
        _global_fetcher = DataFetcher()
    return _global_fetcher

# 便捷函数接口
def get_stock_daily_data(stock_code, count=None):
    """获取个股日线数据"""
    return get_fetcher().get_stock_daily_data(stock_code, count)

def get_stock_weekly_data(stock_code, count=None):
    """获取个股周线数据"""
    return get_fetcher().get_stock_weekly_data(stock_code, count)

def get_stock_30min_data(stock_code, count=None):
    """获取个股30分钟数据"""
    return get_fetcher().get_stock_30min_data(stock_code, count)

def get_stock_5min_data(stock_code, count=None):
    """获取个股5分钟数据"""
    return get_fetcher().get_stock_5min_data(stock_code, count)

def get_capital_flow_data(stock_code, days=None):
    """获取资金面数据"""
    return get_fetcher().get_capital_flow_data(stock_code, days)

def get_block_daily_data(block_code, count=None):
    """获取板块指数日K线数据"""
    return get_fetcher().get_block_daily_data(block_code, count)



def connect_miniqmt():
    """连接MiniQMT"""
    return get_fetcher().connect_miniqmt()

def disconnect_miniqmt():
    """断开MiniQMT连接"""
    return get_fetcher().disconnect_miniqmt()


def main():
    """主函数 - 调试功能"""
    import sys

    print("=" * 60)
    print("ZZfetch.py - 股票数据获取模块调试工具")
    print("=" * 60)

    # 获取股票代码 - 支持命令行参数或交互式输入
    if len(sys.argv) >= 2:
        # 命令行参数模式
        code_input = sys.argv[1].strip()
    else:
        # 交互式输入模式
        print("请输入6位股票代码或指数板块代码：")
        code_input = input().strip()

    if not code_input:
        print("错误: 代码不能为空")
        sys.exit(1)

    # 清理和验证代码
    config = DataConfig()
    clean_code = config.clean_stock_code(code_input)

    if len(clean_code) != 6:
        print(f"错误: 无效的代码格式 '{code_input}'，请输入6位数字代码")
        sys.exit(1)

    # 判断代码类型
    is_stock = config.is_stock_code(clean_code)
    is_block = config.is_block_code(clean_code)

    if not is_stock and not is_block:
        print(f"警告: 代码 '{clean_code}' 格式不符合常见股票或板块代码规范，但仍将尝试获取数据")

    code_type = "股票代码" if is_stock else "板块代码" if is_block else "未知类型代码"
    print(f"\n识别为: {code_type} - {clean_code}")

    # 获取数据
    fetcher = get_fetcher()

    try:
        if is_stock or not is_block:  # 股票代码或未知类型当作股票处理
            print(f"\n正在获取股票 {clean_code} 的数据...")

            # 连接MiniQMT
            if fetcher.connect_miniqmt():
                # 获取各种K线数据（接口获取更多数据，控制台显示5条）
                print("\n1. 获取日线数据...")
                daily_data = fetcher.get_stock_daily_data(clean_code, 30)  # 获取30条，显示5条

                print("\n2. 获取周线数据...")
                weekly_data = fetcher.get_stock_weekly_data(clean_code, 20)  # 获取20条，显示5条

                print("\n3. 获取30分钟数据...")
                min30_data = fetcher.get_stock_30min_data(clean_code, 50)  # 获取50条，显示5条

                print("\n4. 获取5分钟数据...")
                min5_data = fetcher.get_stock_5min_data(clean_code, 100)  # 获取100条，显示5条
            else:
                print("MiniQMT连接失败，跳过K线数据获取")

            # 获取资金面数据（接口获取更多数据，控制台显示3条）
            print("\n5. 获取资金面数据...")
            capital_data = fetcher.get_capital_flow_data(clean_code, 10)  # 获取10条，显示3条

        else:  # 板块代码
            print(f"\n正在获取板块 {clean_code} 的数据...")

            # 板块数据优先从TDX文件获取（与原始程序逻辑一致）
            print("\n1. 获取板块日线数据（TDX）...")
            block_data = fetcher.get_block_daily_data(clean_code, 30)  # 获取30条，显示5条

            if block_data is not None and not block_data.empty:
                print(f"? 成功从TDX获取板块日线数据: {len(block_data)} 条记录")
                daily_data = block_data  # 将TDX数据赋值给daily_data变量
            else:
                print("? TDX板块数据获取失败，尝试MiniQMT...")
                # 备用方案：尝试MiniQMT
                if fetcher.connect_miniqmt():
                    print("\n1.1 获取板块日线数据（MiniQMT）...")
                    daily_data = fetcher.get_stock_daily_data(clean_code, 30)  # 获取30条，显示5条

                    print("\n1.2 获取板块30分钟数据（MiniQMT）...")
                    min30_data = fetcher.get_stock_30min_data(clean_code, 50)  # 获取50条，显示5条

        print(f"\n{clean_code} 数据获取完成！")

    except Exception as e:
        print(f"获取数据时出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

    # 断开连接
    fetcher.disconnect_miniqmt()
    print("\n程序退出，感谢使用！")


def test_all_functions():
    """测试所有功能的函数"""
    print("=" * 60)
    print("ZZfetch.py - 功能测试")
    print("=" * 60)

    test_codes = [
        ("000001", "股票"),  # 平安银行
        ("600000", "股票"),  # 浦发银行
        ("880001", "板块"),  # 概念板块
        ("399001", "板块"),  # 深证成指
    ]

    fetcher = get_fetcher()

    for code, code_type in test_codes:
        print(f"\n{'='*40}")
        print(f"测试 {code_type} 代码: {code}")
        print(f"{'='*40}")

        try:
            if code_type == "股票":
                # 连接MiniQMT
                if fetcher.connect_miniqmt():
                    # 测试各种数据获取
                    daily_data = fetcher.get_stock_daily_data(code, 5)
                    weekly_data = fetcher.get_stock_weekly_data(code, 3)
                    min30_data = fetcher.get_stock_30min_data(code, 10)
                    min5_data = fetcher.get_stock_5min_data(code, 20)

                capital_data = fetcher.get_capital_flow_data(code, 2)

            else:  # 板块
                block_data = fetcher.get_block_daily_data(code, 5)

            print(f"{code} 测试完成")

        except Exception as e:
            print(f"测试 {code} 时出错: {e}")

    fetcher.disconnect_miniqmt()
    print("\n所有测试完成！")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_all_functions()
        else:
            main()
    else:
        main()

