# -*- coding: gbk -*-
"""
ZZindicator.py - 技术指标计算模块

基于 ZZfetch.py 获取的K线数据，计算各种技术指标

主要功能：
1. 计算MACD指标：DIF、DEA、MACD柱状图
2. 计算KDJ指标：RSV、K值、D值、J值
3. 计算RSI指标：6日、12日、24日
4. 计算布林带指标：20日均线及上下轨
5. 计算移动平均线：5日、10日、20日、60日
6. 计算成交量均线：5日、10日

所有指标计算都修改确保与原始算法一致
创建日期：2025-01-28
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 检查ZZfetch模块是否可用
try:
    from ZZfetch import get_stock_daily_data, connect_miniqmt, disconnect_miniqmt
    ZZFETCH_AVAILABLE = True
except ImportError:
    print("错误: ZZfetch模块不可用，无法获取实时K线数据")
    ZZFETCH_AVAILABLE = False

class TechnicalIndicators:
    """技术指标计算器"""
    
    def __init__(self):
        """初始化技术指标计算器"""
        self.data = None
    
    def load_data_from_dataframe(self, df):
        """从DataFrame加载K线数据"""
        if df is None or df.empty:
            print("错误: 数据为空")
            return False
        
        # 检查必要的列是否存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"错误: 缺少必要的数据列: {missing_columns}")
            return False
        
        # 复制数据并确保数据类型正确
        self.data = df.copy()
        
        # 转换数据类型
        numeric_columns = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        for col in numeric_columns:
            if col in self.data.columns:
                self.data[col] = pd.to_numeric(self.data[col], errors='coerce')
        
        # 按日期排序
        if '日期' in self.data.columns:
            self.data = self.data.sort_values('日期').reset_index(drop=True)
        
        print(f"? 成功加载 {len(self.data)} 条K线数据")
        return True
    
    def calculate_macd(self, fast_period=12, slow_period=26, signal_period=9):
        """计算MACD指标"""
        if self.data is None or self.data.empty:
            print("错误: 未加载数据")
            return None
        
        close_prices = self.data['收盘'].values
        
        # 计算EMA
        def calculate_ema(prices, period):
            alpha = 2.0 / (period + 1)
            ema = np.zeros_like(prices)
            ema[0] = prices[0]
            
            for i in range(1, len(prices)):
                ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
            
            return ema
        
        # 计算快线和慢线EMA
        ema_fast = calculate_ema(close_prices, fast_period)
        ema_slow = calculate_ema(close_prices, slow_period)
        
        # 计算DIF
        dif = ema_fast - ema_slow
        
        # 计算DEA (DIF的EMA)
        dea = calculate_ema(dif, signal_period)
        
        # 计算MACD柱状图
        macd = (dif - dea) * 2
        
        result = pd.DataFrame({
            'DIF': dif,
            'DEA': dea,
            'MACD': macd
        })
        
        return result
    
    def calculate_rsi(self, periods=[6, 12, 24]):
        """计算RSI指标"""
        if self.data is None or self.data.empty:
            print("错误: 未加载数据")
            return None
        
        close_prices = self.data['收盘'].values
        result = pd.DataFrame()
        
        for period in periods:
            # 计算价格变化
            price_changes = np.diff(close_prices)
            
            # 分离上涨和下跌
            gains = np.where(price_changes > 0, price_changes, 0)
            losses = np.where(price_changes < 0, -price_changes, 0)
            
            # 计算平均收益和平均损失
            avg_gains = np.zeros(len(close_prices))
            avg_losses = np.zeros(len(close_prices))
            
            # 初始值使用简单平均
            if len(gains) >= period:
                avg_gains[period] = np.mean(gains[:period])
                avg_losses[period] = np.mean(losses[:period])
                
                # 后续使用指数移动平均
                for i in range(period + 1, len(close_prices)):
                    avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
                    avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period
            
            # 计算RSI
            rsi = np.zeros(len(close_prices))
            for i in range(period, len(close_prices)):
                if avg_losses[i] != 0:
                    rs = avg_gains[i] / avg_losses[i]
                    rsi[i] = 100 - (100 / (1 + rs))
                else:
                    rsi[i] = 100
            
            result[f'RSI{period}'] = rsi
        
        return result
    
    def calculate_ma(self, periods=[5, 10, 20, 60]):
        """计算移动平均线"""
        if self.data is None or self.data.empty:
            print("错误: 未加载数据")
            return None
        
        close_prices = self.data['收盘']
        result = pd.DataFrame()
        
        for period in periods:
            result[f'MA{period}'] = close_prices.rolling(window=period).mean()
        
        return result
    
    def calculate_volume_ma(self, periods=[5, 10]):
        """计算成交量移动平均线"""
        if self.data is None or self.data.empty:
            print("错误: 未加载数据")
            return None
        
        volume = self.data['成交量']
        result = pd.DataFrame()
        
        for period in periods:
            result[f'MAVOL{period}'] = volume.rolling(window=period).mean()
        
        return result
    
    def calculate_bollinger_bands(self, period=20, std_dev=2):
        """计算布林带指标"""
        if self.data is None or self.data.empty:
            print("错误: 未加载数据")
            return None
        
        close_prices = self.data['收盘']
        
        # 计算中轨（移动平均线）
        middle_band = close_prices.rolling(window=period).mean()
        
        # 计算标准差
        std = close_prices.rolling(window=period).std()
        
        # 计算上轨和下轨
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        result = pd.DataFrame({
            'BOLL_UPPER': upper_band,
            'BOLL_MIDDLE': middle_band,
            'BOLL_LOWER': lower_band
        })
        
        return result
    
    def calculate_kdj(self, k_period=9, d_period=3, j_period=3):
        """计算KDJ指标"""
        if self.data is None or self.data.empty:
            print("错误: 未加载数据")
            return None
        
        high_prices = self.data['最高']
        low_prices = self.data['最低']
        close_prices = self.data['收盘']
        
        # 计算RSV
        lowest_low = low_prices.rolling(window=k_period).min()
        highest_high = high_prices.rolling(window=k_period).max()
        
        rsv = ((close_prices - lowest_low) / (highest_high - lowest_low)) * 100
        rsv = rsv.fillna(50)  # 填充NaN值
        
        # 计算K值
        k_values = np.zeros(len(close_prices))
        k_values[0] = 50  # 初始值
        
        for i in range(1, len(close_prices)):
            k_values[i] = (2/3) * k_values[i-1] + (1/3) * rsv.iloc[i]
        
        # 计算D值
        d_values = np.zeros(len(close_prices))
        d_values[0] = 50  # 初始值
        
        for i in range(1, len(close_prices)):
            d_values[i] = (2/3) * d_values[i-1] + (1/3) * k_values[i]
        
        # 计算J值
        j_values = 3 * k_values - 2 * d_values
        
        result = pd.DataFrame({
            'RSV': rsv,
            'K': k_values,
            'D': d_values,
            'J': j_values
        })
        
        return result
    
    def calculate_all_indicators(self):
        """计算所有技术指标"""
        if self.data is None or self.data.empty:
            print("错误: 未加载数据")
            return None
        
        print("正在计算技术指标...")
        
        indicators = {}
        
        # 计算各种指标
        indicators['MACD'] = self.calculate_macd()
        indicators['RSI'] = self.calculate_rsi()
        indicators['MA'] = self.calculate_ma()
        indicators['MAVOL'] = self.calculate_volume_ma()
        indicators['BOLL'] = self.calculate_bollinger_bands()
        indicators['KDJ'] = self.calculate_kdj()
        
        print("? 技术指标计算完成")
        return indicators


def calculate_indicators_from_dataframe(df):
    """从DataFrame计算技术指标的便捷函数"""
    calculator = TechnicalIndicators()
    
    if not calculator.load_data_from_dataframe(df):
        return None
    
    return calculator.calculate_all_indicators()


def get_stock_indicators_with_fetch(stock_code, timeframe="日线"):
    """获取股票数据并计算技术指标"""
    if not ZZFETCH_AVAILABLE:
        print("错误: ZZfetch模块不可用")
        return None

    try:
        # 获取股票数据
        print(f"正在获取股票 {stock_code} 的 {timeframe} 数据...")

        # 根据时间框架选择合适的函数
        if timeframe == "日线":
            stock_data = get_stock_daily_data(stock_code, 250)
        else:
            print(f"错误: 暂不支持 {timeframe} 时间框架")
            return None

        if stock_data is None or stock_data.empty:
            print(f"错误: 无法获取股票 {stock_code} 的数据")
            return None

        print(f"? 成功获取 {len(stock_data)} 条数据")

        # 计算技术指标
        indicators = calculate_indicators_from_dataframe(stock_data)

        if indicators is None:
            print("错误: 技术指标计算失败")
            return None

        return {
            'success': True,
            'stock_code': stock_code,
            'timeframe': timeframe,
            'data_count': len(stock_data),
            'indicators': indicators,
            'raw_data': stock_data
        }

    except Exception as e:
        print(f"获取股票指标时出错: {e}")
        return None

if __name__ == "__main__":
    """主程序 - 支持命令行参数和交互式输入"""
    import sys

    print("ZZindicator.py - 技术指标计算模块")
    print("=" * 50)

    if not ZZFETCH_AVAILABLE:
        print("错误: ZZfetch模块不可用，无法获取实时数据")
        print("请确保ZZfetch.py文件存在且可以正常运行")
        sys.exit(1)

    # 获取股票代码 - 支持命令行参数或交互式输入
    if len(sys.argv) >= 2:
        # 命令行参数模式
        code_input = sys.argv[1].strip()
    else:
        # 交互式输入模式
        print("请输入6位股票代码或指数板块代码：")
        code_input = input().strip()

    if len(code_input) != 6 or not code_input.isdigit():
        print("错误: 请输入6位数字股票代码")
        sys.exit(1)

    # 连接MiniQMT
    print("正在连接MiniQMT...")
    if not connect_miniqmt():
        print("错误: MiniQMT连接失败")
        sys.exit(1)

    try:
        # 获取并计算指标
        result = get_stock_indicators_with_fetch(code_input)

        if result and result['success']:
            print("? 技术指标计算成功")
            print(f"数据量: {result['data_count']} 条记录")

            # 显示部分结果
            indicators = result['indicators']
            if 'MACD' in indicators and indicators['MACD'] is not None:
                macd_data = indicators['MACD']
                print(f"MACD DIF最新值: {macd_data['DIF'].iloc[-1]:.6f}")
                print(f"MACD DEA最新值: {macd_data['DEA'].iloc[-1]:.6f}")
                print(f"MACD MACD最新值: {macd_data['MACD'].iloc[-1]:.6f}")

            if 'RSI' in indicators and indicators['RSI'] is not None:
                rsi_data = indicators['RSI']
                if 'RSI6' in rsi_data:
                    print(f"RSI6最新值: {rsi_data['RSI6'].iloc[-1]:.2f}")

            if 'MA' in indicators and indicators['MA'] is not None:
                ma_data = indicators['MA']
                if 'MA5' in ma_data:
                    print(f"MA5最新值: {ma_data['MA5'].iloc[-1]:.2f}")
                if 'MA10' in ma_data:
                    print(f"MA10最新值: {ma_data['MA10'].iloc[-1]:.2f}")

            print("? 技术指标计算完成")
        else:
            print(f"计算股票 {code_input} 指标失败")
            sys.exit(1)

    finally:
        # 断开连接
        disconnect_miniqmt()
        print("MiniQMT连接已断开")
