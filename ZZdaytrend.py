# -*- coding: gbk -*-
'''
ZZdaytrend.py - 股票/板块指数趋势评分程序

实现股票/板块指数的趋势评分功能，基于技术信号和成交量分析

主要功能：
1. 接收6位股票代码或板块指数代码作为输入参数
2. 基于趋势阶段判定计算阶段分（0-2分）
3. 基于趋势强度判定计算强度分（0-2分）
4. 输出最终评分（0分或1分）及详细评分依据

评分规则：
- 阶段分 + 强度分 ≥ 3时：最终得分1分
- 其他情况：最终得分0分

信号优先级规则：
1. 阶段结束信号具有最高优先级，始终优先于其他信号
2. 当开始阶段信号与确认阶段信号同时出现时：
   - 比较信号数量：确认阶段信号数量 > 开始阶段信号数量 → 采用确认阶段
   - 比较信号数量：开始阶段信号数量 > 确认阶段信号数量 → 采用开始阶段
   - 信号数量相等时：默认采用开始阶段信号

作者：基于ZZsignal.py、ZZindicator.py、ZZfetch.py开发
日期：2025-01-28
更新：2025-01-28 - 添加信号数量对比优先级规则
'''

import sys
import os
from datetime import datetime, timedelta

# 添加项目目录到路径688
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入依赖模块
try:
    from ZZsignal import TechnicalSignalDetector, get_stock_data
    ZZSIGNAL_AVAILABLE = True
except ImportError:
    ZZSIGNAL_AVAILABLE = False
    print("错误: ZZsignal模块未找到")

try:
    from ZZindicator import calculate_indicators_from_dataframe
    ZZINDICATOR_AVAILABLE = True
except ImportError:
    ZZINDICATOR_AVAILABLE = False
    print("错误: ZZindicator模块未找到")

try:
    from ZZfetch import get_fetcher, connect_miniqmt, disconnect_miniqmt
    ZZFETCH_AVAILABLE = True
except ImportError:
    ZZFETCH_AVAILABLE = False
    print("错误: ZZfetch模块未找到")


class TrendScorer:
    '''趋势评分器'''
    
    def __init__(self):
        '''初始化趋势评分器'''
        self.stock_code = None
        self.data = None
        self.signals = []
        self.stage_score = 0
        self.strength_score = 0
        self.final_score = 0
        self.stage_reason = ""
        self.strength_reason = ""
        
        # 趋势阶段信号定义
        self.trend_start_signals = [
            "DIF拐头向上",
            "RSI6上穿50", 
            "MA5拐头向上",
            "MACD金叉",
            "MA5上穿MA10"
        ]
        
        self.trend_confirm_signals = [
            "DIF值持续上涨",
            "DIF持续上涨",  # 添加缺失的信号名称
            "MACD红柱持续变长",
            "股价持续运行在MA5之上",
            "RSI6始终运行在50以上"
        ]
        
        self.trend_end_signals = [
            "DIF拐头向下",
            "DIF值持续下降",
            "DIF持续下降"  # 添加缺失的信号名称
        ]
        
        # 趋势强度信号定义
        self.strong_trend_signals = [
            "放量超过MAVOL5两倍以上",
            "3根及以上K线的MAVOL5上涨",
            "4根及以上K线的VOL大于MAVOL5"
        ]
        
        self.weak_trend_signals = [
            "3根及以上K线的MAVOL5下跌",
            "4根及以上K线的VOL小于MAVOL5"
        ]
    
    def load_stock_data(self, stock_code, timeframe="日线"):
        '''加载股票数据'''
        if not ZZFETCH_AVAILABLE:
            print("错误: ZZfetch模块不可用")
            return False
        
        self.stock_code = stock_code
        print(f"正在获取股票 {stock_code} 的数据...")
        
        # 获取股票数据
        self.data = get_stock_data(stock_code, timeframe)
        if self.data is None or self.data.empty:
            print(f"错误: 无法获取股票 {stock_code} 的数据")
            return False
        
        print(f"? 成功获取 {len(self.data)} 条数据")
        return True
    
    def detect_signals(self):
        '''检测技术信号'''
        if not ZZSIGNAL_AVAILABLE:
            print("错误: ZZsignal模块不可用")
            return False
        
        if self.data is None:
            print("错误: 未加载数据")
            return False
        
        print("正在检测技术信号...")
        
        # 创建信号检测器
        detector = TechnicalSignalDetector()
        
        # 加载数据
        if not detector.load_data(self.data, "日线"):
            print("错误: 信号检测器数据加载失败")
            return False
        
        # 计算技术指标
        if not detector.calculate_indicators():
            print("错误: 技术指标计算失败")
            return False
        
        # 检测所有信号
        self.signals = detector.detect_all_signals()
        
        print(f"? 检测到 {len(self.signals)} 个技术信号")
        return True
    
    def get_recent_signals(self, periods=5):
        '''获取最近N个交易周期的信号'''
        if not self.signals:
            return []
        
        # 获取最近N个交易日的日期列表，转换为字符串格式便于比较
        recent_dates = self.data['日期'].tail(periods).tolist()
        recent_date_strs = []
        for date in recent_dates:
            if hasattr(date, 'strftime'):
                recent_date_strs.append(date.strftime('%Y-%m-%d'))
            else:
                recent_date_strs.append(str(date)[:10])  # 取前10个字符作为日期

        recent_signals = []
        for signal in self.signals:
            signal_time = signal.signal_time

            # 转换信号时间为字符串格式
            signal_date_str = ""
            if hasattr(signal_time, 'strftime'):
                signal_date_str = signal_time.strftime('%Y-%m-%d')
            else:
                # 处理numpy.datetime64类型
                signal_date_str = str(signal_time)[:10]

            # 检查信号日期是否在最近的交易日内
            if signal_date_str in recent_date_strs:
                recent_signals.append(signal)

        return recent_signals

    def display_all_signals(self):
        '''显示所有检测到的信号'''
        recent_signals = self.get_recent_signals(5)

        print(f"\n=== 信号检测结果 ===")
        print(f"最近5个交易周期内检测到的所有信号：")

        if not recent_signals:
            print("  未检测到任何技术信号")
            return

        # 按时间排序显示所有信号
        sorted_signals = sorted(recent_signals, key=lambda x: x.signal_time, reverse=True)

        for i, signal in enumerate(sorted_signals, 1):
            signal_time_str = ""
            if hasattr(signal.signal_time, 'strftime'):
                signal_time_str = signal.signal_time.strftime('%Y-%m-%d')
            else:
                signal_time_str = str(signal.signal_time)[:10]

            print(f"  {i}. {signal.signal_name} ({signal_time_str})")

        print(f"共检测到 {len(recent_signals)} 个信号")

    def calculate_stage_score(self):
        '''计算阶段分'''
        recent_signals = self.get_recent_signals(5)

        if not recent_signals:
            self.stage_score = 0
            self.stage_reason = "未检测到最近5个周期的技术信号"
            return

        # 分类信号
        start_signals = []
        confirm_signals = []
        end_signals = []
        unclassified_signals = []  # 未分类的信号

        for signal in recent_signals:
            signal_name = signal.signal_name
            if signal_name in self.trend_start_signals:
                start_signals.append(signal)
            elif signal_name in self.trend_confirm_signals:
                confirm_signals.append(signal)
            elif signal_name in self.trend_end_signals:
                end_signals.append(signal)
            else:
                unclassified_signals.append(signal)

        print(f"\\n=== 阶段分计算 ===")
        print(f"开始阶段信号: {len(start_signals)} 个")
        print(f"确认阶段信号: {len(confirm_signals)} 个")
        print(f"结束阶段信号: {len(end_signals)} 个")

        # 显示未分类的信号（用于调试）
        if unclassified_signals:
            print(f"未分类信号: {len(unclassified_signals)} 个")
            for signal in unclassified_signals:
                print(f"  - {signal.signal_name}")

        # 应用优先级规则
        if start_signals and end_signals:
            # 比较最晚出现的信号
            latest_start = max(start_signals, key=lambda x: x.signal_time)
            latest_end = max(end_signals, key=lambda x: x.signal_time)

            if latest_start.signal_time >= latest_end.signal_time:
                # 开始阶段信号更晚
                if confirm_signals:
                    # 新规则：开始阶段信号与确认阶段信号同时出现时，按数量对比
                    start_count = len(start_signals)
                    confirm_count = len(confirm_signals)
                    print(f"信号数量对比: 开始阶段{start_count}个 vs 确认阶段{confirm_count}个")

                    if confirm_count > start_count:
                        self.stage_score = 1  # 确认阶段
                        self.stage_reason = f"确认阶段信号数量({confirm_count}个)多于开始阶段信号({start_count}个)，判定为确认阶段（1分）"
                    elif start_count > confirm_count:
                        self.stage_score = 2  # 开始阶段
                        self.stage_reason = f"开始阶段信号数量({start_count}个)多于确认阶段信号({confirm_count}个)，判定为开始阶段（2分）"
                    else:
                        # 数量相等，默认采用开始阶段信号
                        self.stage_score = 2  # 开始阶段
                        self.stage_reason = f"开始阶段与确认阶段信号数量相等({start_count}个)，默认采用开始阶段信号（2分）"
                else:
                    self.stage_score = 2  # 开始阶段
                    self.stage_reason = f"最晚信号为开始阶段：{latest_start.signal_name}（2分）"
            else:
                # 结束阶段信号更晚，结束信号具有最高优先级
                self.stage_score = 0  # 结束阶段
                self.stage_reason = f"最晚信号为结束阶段：{latest_end.signal_name}，结束信号具有最高优先级（0分）"
        elif start_signals:
            if confirm_signals:
                # 新规则：开始阶段信号与确认阶段信号同时出现时，按数量对比
                start_count = len(start_signals)
                confirm_count = len(confirm_signals)
                print(f"信号数量对比: 开始阶段{start_count}个 vs 确认阶段{confirm_count}个")

                if confirm_count > start_count:
                    self.stage_score = 1  # 确认阶段
                    self.stage_reason = f"确认阶段信号数量({confirm_count}个)多于开始阶段信号({start_count}个)，判定为确认阶段（1分）"
                elif start_count > confirm_count:
                    self.stage_score = 2  # 开始阶段
                    self.stage_reason = f"开始阶段信号数量({start_count}个)多于确认阶段信号({confirm_count}个)，判定为开始阶段（2分）"
                else:
                    # 数量相等，默认采用开始阶段信号
                    self.stage_score = 2  # 开始阶段
                    self.stage_reason = f"开始阶段与确认阶段信号数量相等({start_count}个)，默认采用开始阶段信号（2分）"
            else:
                self.stage_score = 2  # 开始阶段
                self.stage_reason = f"检测到开始阶段信号：{[s.signal_name for s in start_signals]}（2分）"
        elif confirm_signals and end_signals:
            # 结束阶段信号具有最高优先级
            self.stage_score = 0  # 结束阶段
            self.stage_reason = f"确认阶段与结束阶段信号同时出现，结束信号具有最高优先级（0分）"
        elif confirm_signals:
            self.stage_score = 1  # 确认阶段
            self.stage_reason = f"检测到确认阶段信号：{[s.signal_name for s in confirm_signals]}（1分）"
        elif end_signals:
            self.stage_score = 0  # 结束阶段
            self.stage_reason = f"检测到结束阶段信号：{[s.signal_name for s in end_signals]}（0分）"
        else:
            # 新规则：如果没有任何开始阶段、确认阶段、结束阶段信号，视为获得1分
            self.stage_score = 1
            self.stage_reason = "未检测到相关趋势阶段信号，按规则视为中性阶段（1分）"

        print(f"阶段分: {self.stage_score}分")
        print(f"判断依据: {self.stage_reason}")

    def calculate_strength_score(self):
        '''计算强度分'''
        recent_signals = self.get_recent_signals(5)

        if not recent_signals:
            self.strength_score = 1  # 中性
            self.strength_reason = "未检测到最近5个周期的成交量信号，判定为中性（1分）"
            return

        # 分类信号
        strong_signals = []
        weak_signals = []

        for signal in recent_signals:
            signal_name = signal.signal_name
            if signal_name in self.strong_trend_signals:
                strong_signals.append(signal)
            elif signal_name in self.weak_trend_signals:
                weak_signals.append(signal)

        print(f"\\n=== 强度分计算 ===")
        print(f"强势信号: {len(strong_signals)} 个")
        print(f"弱势信号: {len(weak_signals)} 个")

        # 判断强度
        if strong_signals and not weak_signals:
            self.strength_score = 2  # 强势
            self.strength_reason = f"检测到强势信号：{[s.signal_name for s in strong_signals]}（2分）"
        elif weak_signals and not strong_signals:
            self.strength_score = 0  # 弱势
            self.strength_reason = f"检测到弱势信号：{[s.signal_name for s in weak_signals]}（0分）"
        elif strong_signals and weak_signals:
            # 同时存在强势和弱势信号，比较数量
            if len(strong_signals) > len(weak_signals):
                self.strength_score = 2
                self.strength_reason = f"强势信号({len(strong_signals)}个)多于弱势信号({len(weak_signals)}个)，判定为强势（2分）"
            elif len(weak_signals) > len(strong_signals):
                self.strength_score = 0
                self.strength_reason = f"弱势信号({len(weak_signals)}个)多于强势信号({len(strong_signals)}个)，判定为弱势（0分）"
            else:
                self.strength_score = 1
                self.strength_reason = f"强势信号与弱势信号数量相等，判定为中性（1分）"
        else:
            self.strength_score = 1  # 中性
            self.strength_reason = "未检测到相关成交量强度信号，判定为中性（1分）"

        print(f"强度分: {self.strength_score}分")
        print(f"判断依据: {self.strength_reason}")

    def calculate_final_score(self):
        '''计算最终评分'''
        total_score = self.stage_score + self.strength_score

        if total_score >= 3:
            self.final_score = 1
        else:
            self.final_score = 0

        print(f"\\n=== 最终评分 ===")
        print(f"阶段分: {self.stage_score}分")
        print(f"强度分: {self.strength_score}分")
        print(f"总分: {total_score}分")
        print(f"最终评分: {self.final_score}分")

        return self.final_score

    def analyze(self):
        '''执行完整的趋势分析'''
        print(f"\\n开始分析股票 {self.stock_code} 的趋势评分...")

        # 显示所有检测到的信号
        self.display_all_signals()

        # 计算阶段分
        self.calculate_stage_score()

        # 计算强度分
        self.calculate_strength_score()

        # 计算最终评分
        self.calculate_final_score()

        # 返回结果
        return {
            'stock_code': self.stock_code,
            'stage_score': self.stage_score,
            'stage_reason': self.stage_reason,
            'strength_score': self.strength_score,
            'strength_reason': self.strength_reason,
            'final_score': self.final_score,
            'total_score': self.stage_score + self.strength_score
        }


def analyze_stock_trend(stock_code):
    '''分析股票趋势评分的便捷函数'''
    scorer = TrendScorer()
    
    # 加载数据
    if not scorer.load_stock_data(stock_code):
        return None
    
    # 检测信号
    if not scorer.detect_signals():
        return None
    
    # 执行分析
    return scorer.analyze()


if __name__ == "__main__":
    import sys

    print("股票趋势评分程序")

    # 获取股票代码 - 支持命令行参数或交互式输入
    if len(sys.argv) >= 2:
        # 命令行参数模式
        stock_code = sys.argv[1].strip()
    else:
        # 交互式输入模式
        print("请输入6位股票代码或指数板块代码：")
        stock_code = input().strip()

    if len(stock_code) != 6 or not stock_code.isdigit():
        print("错误: 请输入有效的6位股票代码")
        sys.exit(1)

    result = analyze_stock_trend(stock_code)
    if result:
        print(f"\n股票 {stock_code} 趋势评分结果：")
        print(f"最终评分：{result['final_score']}分")
        print(f"阶段分：{result['stage_score']}分 - {result['stage_reason']}")
        print(f"强度分：{result['strength_score']}分 - {result['strength_reason']}")
    else:
        print(f"分析股票 {stock_code} 失败")
        sys.exit(1)
